---
version: 30
current:
  head_commit: 59caad3d624e0085031ac4a7ead07925916a16a4
  files:
    "{latest}/bin/sync-to-bastion.sh":
      file_hash: f63cb7bc88bc55fe67319a0f71dcc399c7e55514
      from_commit: b8c397817799c39d58e6609e297b50a00bf5eda8
      message: "fix: move to tofu"
    "{latest}/ci/tasks/workers.sh":
      file_hash: 35d2cef76b061409137dc62145f1bc1f71a6543e
      from_commit: 61e33e455066585f701cb076d8d49fe1b295ae0a
      message: "refactor  switch to tofu (#7554)"
    "{latest}/gcp/galoy-staging/shared/main.tf":
      file_hash: 527adbffba2d570191c7ad5b7c10a171e34e8b50
      from_commit: 59caad3d624e0085031ac4a7ead07925916a16a4
      message: " chore: match the staging domain pattern to prod (#8087)"
    "{latest}/gcp/galoy-staging/workers/main.tf":
      file_hash: 695f218deae6ec6aeb58cc40dd43e56f9c8672fe
      from_commit: 34e2a204eef64872960971d02eeb9039a85c822e
      message: "chore: remove leftover params from staging workers"
    "{latest}/gcp/galoy-staging/workers/workers-scaling.yml":
      file_hash: 51f76c03a3d771761460ede04f8677982f7d7b78
      from_commit: 9d3313da6c2ffdcfa596fe92be610ce14e374e52
      message: "feat: vm based workers (#6742)"
    "{latest}/modules/services/workers/main.tf":
      file_hash: fa3e02a06af464ba62c724f78f88d28ec0d2fd93
      from_commit: 9d3313da6c2ffdcfa596fe92be610ce14e374e52
      message: "feat: vm based workers (#6742)"
    "{latest}/modules/services/workers/workers-values.yml.tmpl":
      file_hash: dac1c47f2c82a8ac0e00a9bedb7b7c8324805f02
      from_commit: a7da6ee2584f3a0e75967d2d46f238abf2e85867
      message: "chore: use containerd runtime"
propagation_queue:
  - head_commit: efe842644097b301e6cff948772c592a80812d41
    files:
      "{latest}/bin/sync-to-bastion.sh":
        file_hash: f63cb7bc88bc55fe67319a0f71dcc399c7e55514
        from_commit: b8c397817799c39d58e6609e297b50a00bf5eda8
        message: "fix: move to tofu"
      "{latest}/ci/tasks/workers.sh":
        file_hash: 35d2cef76b061409137dc62145f1bc1f71a6543e
        from_commit: 61e33e455066585f701cb076d8d49fe1b295ae0a
        message: "refactor  switch to tofu (#7554)"
      "{latest}/gcp/galoy-staging/shared/main.tf":
        file_hash: ddf53cabe701956c5e380b8750656c8870b708b6
        from_commit: efe842644097b301e6cff948772c592a80812d41
        message: "chore: switch staging to staging.blink.sv, remove staging.galoy.io (#8074)"
      "{latest}/gcp/galoy-staging/workers/main.tf":
        file_hash: 695f218deae6ec6aeb58cc40dd43e56f9c8672fe
        from_commit: 34e2a204eef64872960971d02eeb9039a85c822e
        message: "chore: remove leftover params from staging workers"
      "{latest}/gcp/galoy-staging/workers/workers-scaling.yml":
        file_hash: 51f76c03a3d771761460ede04f8677982f7d7b78
        from_commit: 9d3313da6c2ffdcfa596fe92be610ce14e374e52
        message: "feat: vm based workers (#6742)"
      "{latest}/modules/services/workers/main.tf":
        file_hash: fa3e02a06af464ba62c724f78f88d28ec0d2fd93
        from_commit: 9d3313da6c2ffdcfa596fe92be610ce14e374e52
        message: "feat: vm based workers (#6742)"
      "{latest}/modules/services/workers/workers-values.yml.tmpl":
        file_hash: dac1c47f2c82a8ac0e00a9bedb7b7c8324805f02
        from_commit: a7da6ee2584f3a0e75967d2d46f238abf2e85867
        message: "chore: use containerd runtime"

