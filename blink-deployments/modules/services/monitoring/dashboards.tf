provider "grafana" {
  url                  = local.grafana_url
  auth                 = "admin:${random_password.grafana.result}"
  insecure_skip_verify = true
}

# Wait for <PERSON><PERSON> to be ready before creating resources
resource "null_resource" "wait_for_grafana" {
  depends_on = [helm_release.monitoring]

  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for <PERSON><PERSON> to be ready..."
      for i in {1..30}; do
        if curl -f -s -k --max-time 10 "${local.grafana_url}/api/health" > /dev/null 2>&1; then
          echo "<PERSON><PERSON> is ready!"
          exit 0
        fi
        echo "Attempt $i: Grafana not ready yet, waiting 10 seconds..."
        sleep 10
      done
      echo "Grafana failed to become ready after 5 minutes"
      exit 1
    EOT
  }
}

# Check for existing data source and get its ID for import
resource "null_resource" "check_existing_datasource" {
  depends_on = [null_resource.wait_for_grafana]

  provisioner "local-exec" {
    command = <<-EOT
      echo "Checking for existing Grafana data source..."

      # Get list of data sources and find the Google Cloud Monitoring one
      DATASOURCE_ID=$(curl -s -k -u "admin:${random_password.grafana.result}" \
        "${local.grafana_url}/api/datasources" | \
        jq -r '.[] | select(.name == "Google Cloud Monitoring") | .id' 2>/dev/null || echo "")

      if [ ! -z "$DATASOURCE_ID" ] && [ "$DATASOURCE_ID" != "null" ]; then
        echo "Found existing data source with ID: $DATASOURCE_ID"
        echo "To import this data source, run:"
        echo "terraform import module.monitoring.grafana_data_source.stackdriver $DATASOURCE_ID"
      else
        echo "No existing data source found with name 'Google Cloud Monitoring'"
      fi
    EOT
  }
}

resource "grafana_data_source" "stackdriver" {
  type = "stackdriver"
  name = "Google Cloud Monitoring"

  json_data {
    token_uri           = local.grafana_sa_token_uri
    client_email        = local.grafana_sa_email
    authentication_type = "jwt"
    default_project     = local.grafana_sa_project_id
  }

  secure_json_data {
    private_key = local.grafana_sa_private_key
  }

  depends_on = [
    helm_release.monitoring,
    null_resource.wait_for_grafana
  ]

  lifecycle {
    create_before_destroy = true
  }
}

resource "grafana_dashboard" "main" {
  overwrite = true
  config_json = templatefile("${path.module}/dashboard.json", {
    gcp_project : local.gcp_project
    title : local.dashboard_title
    graphql_playground_url : local.graphql_playground_url
    graphql_errors_metric : local.graphql_errors_metric
    bitcoin_namespace : local.bitcoin_namespace
    galoy_namespace : local.galoy_namespace
  })

  depends_on = [
    helm_release.monitoring,
    null_resource.wait_for_grafana
  ]

  lifecycle {
    create_before_destroy = true
  }
}
