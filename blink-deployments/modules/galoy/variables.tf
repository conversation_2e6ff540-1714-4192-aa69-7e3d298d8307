variable "name_prefix" {}
variable "gcp_project" {}
variable "gcp_region" {}

variable "secrets" {
  sensitive = true
}

variable "lnd_priority" { default = "lnd1" }

variable "bitcoin_network" {}
variable "funder_user_name" {}
variable "withdraw_method" { default = "flat" }
variable "withdraw_ratio_basis_points_on_imbalance" { default = 20 }
variable "withdraw_min_fee" { default = 10000 }
variable "withdraw_fee_threshold" { default = 1000000 }
variable "max_hot_wallet" { default = ********0 }
variable "request_code_per_ip_points" { default = 64 }
variable "galoy_instance_name" {}
variable "backups_bucket_name" {}
variable "ip_recording_enabled" { default = true }
variable "proxy_checking_enabled" { default = true }
variable "unsecure_default_login_code_enabled" { default = false }

variable "lightning_address_domain" {}
variable "lightning_address_domain_aliases" { default = [] }

variable "quizzes_allow_phone_countries" { default = [] }
variable "quizzes_deny_phone_countries" { default = [] }
variable "quizzes_deny_asns" { default = [] }
variable "accounts_deny_ip_countries" { default = [] }
variable "accounts_deny_phone_countries" { default = [] }
variable "accounts_enable_phone_check" { default = true }
variable "unsupported_countries" { default = [] }
variable "unsupported_sms_countries" { default = [] }
variable "unsupported_whatsapp_countries" { default = [] }
variable "unsupported_telegram_countries" { default = [] }
variable "twilio_messaging_welcome_content_sid" { default = "" }

variable "secondary_domain" {}
variable "tertiary_domain" {}
variable "api_subdomain" { default = "api" }
variable "admin_api_subdomain" { default = "admin-api" }
variable "websocket_subdomain" { default = "ws" }
variable "consent_subdomain" { default = "consent" }
variable "auth_subdomain" { default = "auth" }
variable "smoketest_kubeconfig" {
  sensitive = true
  default   = ""
}
variable "root_domain" {}

variable "extra_cors_allowed_origins" { default = [] }

variable "mongodb_ssd_size" { default = "8Gi" }

variable "accounts_initial_status" {
  default = "active"
}

variable "rebalance_enabled" { default = true }

variable "ha_pg" { default = true }

variable "email_domain" {}

variable "price_history_pg_tier" { default = "db-custom-2-3840" }
variable "price_currency_beacon_enabled" { default = false }
variable "price_exchangeratehost_enabled" { default = false }

variable "api_keys_prefix" {}
variable "api_keys_pg_tier" { default = "db-custom-2-3840" }

variable "notifications_pg_tier" { default = "db-custom-2-3840" }

variable "openai_assistant_id" {}

locals {
  name_prefix = var.name_prefix
  gcp_project = var.gcp_project
  gcp_region  = var.gcp_region
  vpc_name    = "${local.name_prefix}-vpc"

  bitcoin_network     = var.bitcoin_network
  galoy_instance_name = var.galoy_instance_name

  lightning_address_domain         = var.lightning_address_domain
  lightning_address_domain_aliases = var.lightning_address_domain_aliases

  quizzes_allow_phone_countries  = var.quizzes_allow_phone_countries
  quizzes_deny_phone_countries   = var.quizzes_deny_phone_countries
  quizzes_deny_asns              = var.quizzes_deny_asns
  accounts_deny_ip_countries     = var.accounts_deny_ip_countries
  accounts_deny_phone_countries  = var.accounts_deny_phone_countries
  accounts_enable_phone_check    = var.accounts_enable_phone_check
  unsupported_countries          = var.unsupported_countries
  unsupported_sms_countries      = var.unsupported_sms_countries
  unsupported_whatsapp_countries = var.unsupported_whatsapp_countries
  unsupported_telegram_countries = var.unsupported_telegram_countries

  api_limit_rpm              = 360
  api_limit_burst_multiplier = 20
  api_limit_connections      = 120

  funder_user_name                         = var.funder_user_name
  withdraw_method                          = var.withdraw_method
  withdraw_ratio_basis_points_on_imbalance = var.withdraw_ratio_basis_points_on_imbalance
  withdraw_min_fee                         = var.withdraw_min_fee
  withdraw_fee_threshold                   = var.withdraw_fee_threshold
  max_hot_wallet                           = var.max_hot_wallet
  request_code_per_ip_points               = var.request_code_per_ip_points
  withdrawal_level_zero                    = 12500
  withdrawal_level_one                     = 99900
  withdrawal_level_two                     = 5000000
  withdrawal_level_three                   = ********
  intraledger_level_zero                   = 12500
  intraledger_level_one                    = 99900
  intraledger_level_two                    = ********
  intraledger_level_three                  = ********
  trade_intra_account_level_zero           = 200000
  trade_intra_account_level_one            = ********
  trade_intra_account_level_two            = ********
  trade_intra_account_level_three          = ********
  bitcoin_namespace                        = "${local.name_prefix}-bitcoin"
  addons_namespace                         = "${local.name_prefix}-addons"
  stablesats_namespace                     = "${local.name_prefix}-stablesats"
  galoy_namespace                          = "${local.name_prefix}-galoy"
  otel_namespace                           = "${local.name_prefix}-otel"
  smoketest_namespace                      = "${local.name_prefix}-smoketest"
  blink_addons_namespace                   = "${local.name_prefix}-blink-addons"
  smoketest_kubeconfig                     = var.smoketest_kubeconfig

  ip_recording_enabled   = var.ip_recording_enabled
  proxy_checking_enabled = var.proxy_checking_enabled
  proxy_check_api_key    = jsondecode(var.secrets).proxy_check_api_key

  openai_api_key      = jsondecode(var.secrets).openai_api_key
  openai_assistant_id = var.openai_assistant_id
  pinecone_api_key    = jsondecode(var.secrets).pinecone_api_key

  extra_cors_allowed_origins = var.extra_cors_allowed_origins

  lnd1_host    = "lnd1.${local.bitcoin_namespace}.svc.cluster.local"
  lnd2_host    = "lnd2.${local.bitcoin_namespace}.svc.cluster.local"
  lnd_priority = var.lnd_priority

  bria_host = "bria-api.${local.bitcoin_namespace}.svc.cluster.local"

  galoy_endpoint              = local.api_hosts[0]
  price_history_endpoint      = "galoy-price-history.${local.galoy_namespace}.svc.cluster.local"
  otel_exporter_otlp_endpoint = "opentelemetry-collector.${local.otel_namespace}.svc.cluster.local:4318"
  otel_exporter_grpc_endpoint = "opentelemetry-collector.${local.otel_namespace}.svc.cluster.local:4317"

  geetest_key                            = jsondecode(var.secrets).geetest.key
  geetest_id                             = jsondecode(var.secrets).geetest.id
  firebase_service_account               = jsondecode(var.secrets).firebase_sa_creds
  twilio_account_sid                     = jsondecode(var.secrets).twilio.account_sid
  twilio_auth_token                      = jsondecode(var.secrets).twilio.auth_token
  twilio_verify_service_friendly_name    = local.galoy_instance_name
  twilio_messaging_service_friendly_name = local.galoy_instance_name
  twilio_messaging_welcome_content_sid   = var.twilio_messaging_welcome_content_sid

  telegram_private_key                = jsondecode(var.secrets).telegram.private_key
  telegram_api_token                  = jsondecode(var.secrets).telegram.bot_api_token
  backups_sa_creds                    = jsondecode(var.secrets).backups_sa_creds
  aws_access_key                      = jsondecode(var.secrets).aws_access_key
  aws_secret_key                      = jsondecode(var.secrets).aws_secret_key
  backups_bucket_name                 = var.backups_bucket_name
  mongo_user                          = "testGaloy"
  mongo_database                      = "galoy"
  mongodb_ssd_size                    = var.mongodb_ssd_size
  dropbox_access_token                = jsondecode(var.secrets).dropbox_access_token
  mattermost_webhook_url              = jsondecode(var.secrets).mobile_feedback_mattermost_webhook_url
  svix_secret                         = jsondecode(var.secrets).svix_secret
  unsecure_default_login_code_enabled = var.unsecure_default_login_code_enabled

  api_hosts       = ["${var.api_subdomain}.${var.root_domain}", "${var.api_subdomain}.${var.secondary_domain}", "${var.api_subdomain}.${var.tertiary_domain}"]
  websocket_hosts = ["${var.websocket_subdomain}.${var.root_domain}", "${var.websocket_subdomain}.${var.secondary_domain}", "${var.websocket_subdomain}.${var.tertiary_domain}"]
  consent_hosts   = ["${var.consent_subdomain}.${var.secondary_domain}", "${var.consent_subdomain}.${var.root_domain}", "${var.consent_subdomain}.${var.tertiary_domain}"]
  admin_api_hosts = ["${var.admin_api_subdomain}.${var.root_domain}", "${var.admin_api_subdomain}.${var.secondary_domain}"]

  core_via_oathkeeper_proxy = "galoy-oathkeeper-proxy:4455"

  admin_panel_session_verification_url = "http://admin-panel.${local.addons_namespace}.svc.cluster.local:3000/api/auth/session"
  kratos_public_api_url                = "http://galoy-kratos-public.${local.galoy_namespace}.svc.cluster.local"
  kratos_admin_api_url                 = "http://galoy-kratos-admin.${local.galoy_namespace}.svc.cluster.local"

  accounts_initial_status = var.accounts_initial_status
  test_account_numbers = {
    "+***********" = "google",
    "+***********" = "apple",
    "+***********" = "huawei",
  }

  dealer_phone = "+**********"

  android_min_build_number  = 744
  android_last_build_number = 763
  ios_min_build_number      = 731
  ios_last_build_number     = 750

  rebalance_enabled = var.rebalance_enabled

  price_history_pg_instance_name      = "${local.name_prefix}-price-history"
  price_history_pg_tier               = var.price_history_pg_tier
  price_history_pg_dbname             = "price-history"
  price_history_pg_user               = "price-history"
  price_currency_beacon_api_key       = jsondecode(var.secrets).price_currency_beacon_api_key
  price_currency_beacon_enabled       = var.price_currency_beacon_enabled
  price_exchangeratehost_api_key      = jsondecode(var.secrets).price_exchangeratehost_api_key
  price_exchangeratehost_enabled      = var.price_exchangeratehost_enabled
  price_history_tracing_service_name  = "${var.name_prefix}-price-history"
  price_realtime_tracing_service_name = "${var.name_prefix}-price-realtime"

  graphql_hostname    = "${var.api_subdomain}.${var.root_domain}"
  galoy_auth_host     = "${var.auth_subdomain}.${var.root_domain}"
  hydra_public_host   = "oauth.${var.secondary_domain}"
  hydra_cookie_domain = var.secondary_domain

  circles_subgraph_url   = "http://circles.${local.blink_addons_namespace}.svc.cluster.local:4000/graphql"
  blink_kyc_subgraph_url = "http://blink-kyc.${local.blink_addons_namespace}.svc.cluster.local:3000/graphql"

  kratos_admin_host           = "galoy-kratos-admin.${local.galoy_namespace}.svc.cluster.local"
  kratos_pg_dbname            = "kratos"
  kratos_pg_user              = "kratos"
  kratos_cookie_domain        = var.root_domain
  kratos_tracing_service_name = "${var.name_prefix}-kratos"
  kratos_secret_name          = "${var.name_prefix}-kratos"
  kratos_email_domain         = var.email_domain
  kratos_email_sender_name    = local.galoy_instance_name
  kratos_identity_schemas     = ["phone_no_password_v0", "username_password_deviceid_v0", "email_no_password_v0", "phone_email_no_password_v0"]

  oathkeeper_tracing_service_name = "${var.name_prefix}-oathkeeper"
  hydra_tracing_service_name      = "${var.name_prefix}-hydra"

  smtp_password = jsondecode(var.secrets).smtp_password
  smtp_username = jsondecode(var.secrets).smtp_username

  pg_instance_name  = "${local.name_prefix}-galoy"
  ha_pg             = var.ha_pg
  max_connections   = 100
  databases         = ["kratos"]
  big_query_viewers = ["serviceAccount:<EMAIL>"]

  hydra_pg_instance_name = "${local.name_prefix}-hydra"
  hydra_databases        = ["hydra"]

  api_keys_pg_instance_name = "${local.name_prefix}-api-keys"
  api_keys_databases        = ["api-keys"]
  api_keys_prefix           = var.api_keys_prefix
  api_keys_pg_tier          = var.api_keys_pg_tier

  notifications_databases        = ["notifications"]
  notifications_pg_instance_name = "${local.name_prefix}-notifications"
  notifications_pg_tier          = var.notifications_pg_tier

  notifications_from_email = "support@${var.email_domain}"
  notifications_from_name  = var.galoy_instance_name
}
